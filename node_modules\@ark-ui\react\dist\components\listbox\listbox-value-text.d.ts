import { HTMLProps, PolymorphicProps } from '../factory';
import { ForwardRefExoticComponent, RefAttributes } from 'react';
export interface ListboxValueTextBaseProps extends PolymorphicProps {
    /**
     * Text to display when no value is listboxed.
     */
    placeholder?: string | undefined;
}
export interface ListboxValueTextProps extends HTMLProps<'span'>, ListboxValueTextBaseProps {
}
export declare const ListboxValueText: ForwardRefExoticComponent<ListboxValueTextProps & RefAttributes<HTMLSpanElement>>;
