export type { ExpandCollapseDetails, PanelData, ResizeDetails, ResizeEndDetails } from '@zag-js/splitter';
export { SplitterContext as Context, type SplitterContextProps as ContextProps } from './splitter-context';
export { SplitterPanel as Panel, type SplitterPanelBaseProps as PanelBaseProps, type SplitterPanelProps as PanelProps, } from './splitter-panel';
export { SplitterResizeTrigger as ResizeTrigger, type SplitterResizeTriggerBaseProps as ResizeTriggerBaseProps, type SplitterResizeTriggerProps as ResizeTriggerProps, } from './splitter-resize-trigger';
export { SplitterRoot as Root, type SplitterRootBaseProps as RootBaseProps, type SplitterRootProps as RootProps, } from './splitter-root';
export { SplitterRootProvider as RootProvider, type SplitterRootProviderBaseProps as RootProviderBaseProps, type SplitterRootProviderProps as RootProviderProps, } from './splitter-root-provider';
