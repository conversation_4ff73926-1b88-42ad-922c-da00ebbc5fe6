export type { FocusChangeDetails, ValueChangeDetails } from '@zag-js/tabs';
export { TabContent as Content, type TabContentBaseProps as ContentBaseProps, type TabContentProps as ContentProps, } from './tab-content';
export { TabIndicator as Indicator, type TabIndicatorBaseProps as IndicatorBaseProps, type TabIndicatorProps as IndicatorProps, } from './tab-indicator';
export { TabList as List, type TabListBaseProps as ListBaseProps, type TabListProps as ListProps } from './tab-list';
export { TabTrigger as Trigger, type TabTriggerBaseProps as TriggerBaseProps, type TabTriggerProps as TriggerProps, } from './tab-trigger';
export { TabsContext as Context, type TabsContextProps as ContextProps } from './tabs-context';
export { TabsRoot as Root, type TabsRootBaseProps as RootBaseProps, type TabsRootProps as RootProps } from './tabs-root';
export { TabsRootProvider as RootProvider, type TabsRootProviderBaseProps as RootProviderBaseProps, type TabsRootProviderProps as RootProviderProps, } from './tabs-root-provider';
