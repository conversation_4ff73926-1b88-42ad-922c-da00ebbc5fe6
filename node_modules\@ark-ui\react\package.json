{"name": "@ark-ui/react", "type": "module", "version": "5.16.1", "description": "A collection of unstyled, accessible UI components for React, utilizing state machines for seamless interaction.", "keywords": ["accordion", "angle slider", "avatar", "carousel", "checkbox", "clipboard", "collapsible", "color picker", "combobox", "date picker", "dialog", "editable", "field", "fieldset", "file upload", "frame", "hover card", "menu", "number input", "pagination", "pin input", "popover", "portal", "progress", "radio group", "rating group", "segment group", "select", "signature pad", "slider", "splitter", "switch", "tabs", "tags input", "time picker", "timer", "toast", "toggle group", "tooltip", "tree view", "password input"], "license": "MIT", "homepage": "https://ark-ui.com", "repository": {"type": "git", "url": "git+https://github.com/chakra-ui/ark.git", "directory": "packages/react"}, "bugs": {"url": "https://github.com/chakra-ui/ark/issues"}, "files": ["dist"], "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "./anatomy": {"import": {"types": "./dist/components/anatomy.d.ts", "default": "./dist/components/anatomy.js"}, "require": {"types": "./dist/components/anatomy.d.cts", "default": "./dist/components/anatomy.cjs"}}, "./factory": {"import": {"types": "./dist/components/factory.d.ts", "default": "./dist/components/factory.js"}, "require": {"types": "./dist/components/factory.d.cts", "default": "./dist/components/factory.cjs"}}, "./environment": {"import": {"types": "./dist/providers/environment/index.d.ts", "default": "./dist/providers/environment/index.js"}, "require": {"types": "./dist/providers/environment/index.d.cts", "default": "./dist/providers/environment/index.cjs"}}, "./locale": {"import": {"types": "./dist/providers/locale/index.d.ts", "default": "./dist/providers/locale/index.js"}, "require": {"types": "./dist/providers/locale/index.d.cts", "default": "./dist/providers/locale/index.cjs"}}, "./*": {"import": {"types": "./dist/components/*/index.d.ts", "default": "./dist/components/*/index.js"}, "require": {"types": "./dist/components/*/index.d.cts", "default": "./dist/components/*/index.cjs"}}, "./package.json": "./package.json"}, "scripts": {"build": "vite build", "dev": "bun run storybook", "lint": "biome lint ./src", "test": "vitest", "test:coverage": "vitest run --coverage", "test:ci": "vitest --run", "typecheck": "tsc", "storybook": "storybook dev -p 6006", "prepack": "clean-package", "postpack": "clean-package restore", "release-it": "release-it --config ../../release-it.json"}, "publishConfig": {"access": "public"}, "sideEffects": false, "dependencies": {"@internationalized/date": "3.8.2", "@zag-js/accordion": "1.18.2", "@zag-js/angle-slider": "1.18.2", "@zag-js/anatomy": "1.18.2", "@zag-js/auto-resize": "1.18.2", "@zag-js/avatar": "1.18.2", "@zag-js/carousel": "1.18.2", "@zag-js/checkbox": "1.18.2", "@zag-js/clipboard": "1.18.2", "@zag-js/collapsible": "1.18.2", "@zag-js/collection": "1.18.2", "@zag-js/color-picker": "1.18.2", "@zag-js/color-utils": "1.18.2", "@zag-js/combobox": "1.18.2", "@zag-js/core": "1.18.2", "@zag-js/date-picker": "1.18.2", "@zag-js/date-utils": "1.18.2", "@zag-js/dialog": "1.18.2", "@zag-js/dom-query": "1.18.2", "@zag-js/editable": "1.18.2", "@zag-js/file-upload": "1.18.2", "@zag-js/file-utils": "1.18.2", "@zag-js/focus-trap": "1.18.2", "@zag-js/floating-panel": "1.18.2", "@zag-js/highlight-word": "1.18.2", "@zag-js/hover-card": "1.18.2", "@zag-js/i18n-utils": "1.18.2", "@zag-js/listbox": "1.18.2", "@zag-js/menu": "1.18.2", "@zag-js/number-input": "1.18.2", "@zag-js/pagination": "1.18.2", "@zag-js/password-input": "1.18.2", "@zag-js/pin-input": "1.18.2", "@zag-js/popover": "1.18.2", "@zag-js/presence": "1.18.2", "@zag-js/progress": "1.18.2", "@zag-js/qr-code": "1.18.2", "@zag-js/radio-group": "1.18.2", "@zag-js/rating-group": "1.18.2", "@zag-js/react": "1.18.2", "@zag-js/select": "1.18.2", "@zag-js/signature-pad": "1.18.2", "@zag-js/slider": "1.18.2", "@zag-js/splitter": "1.18.2", "@zag-js/steps": "1.18.2", "@zag-js/switch": "1.18.2", "@zag-js/tabs": "1.18.2", "@zag-js/tags-input": "1.18.2", "@zag-js/time-picker": "1.18.2", "@zag-js/timer": "1.18.2", "@zag-js/toast": "1.18.2", "@zag-js/toggle": "1.18.2", "@zag-js/toggle-group": "1.18.2", "@zag-js/tooltip": "1.18.2", "@zag-js/tour": "1.18.2", "@zag-js/tree-view": "1.18.2", "@zag-js/types": "1.18.2", "@zag-js/utils": "1.18.2"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@storybook/addon-a11y": "9.0.15", "@storybook/react-vite": "9.0.15", "@testing-library/dom": "10.4.0", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.3.0", "@testing-library/user-event": "14.6.1", "@types/jsdom": "21.1.7", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "@vitejs/plugin-react": "4.6.0", "clean-package": "2.2.0", "globby": "14.1.0", "happy-dom": "18.0.1", "lucide-react": "0.525.0", "react": "19.1.0", "react-dom": "19.1.0", "react-shadow": "20.6.0", "react-use": "17.6.0", "react-frame-component": "5.2.7", "react-hook-form": "7.57.0", "resize-observer-polyfill": "1.5.1", "storybook": "9.0.15", "typescript": "5.8.3", "vite": "7.0.2", "vite-plugin-dts": "4.5.4", "vitest": "3.2.4", "@vitest/coverage-v8": "3.2.4", "vitest-axe": "1.0.0-pre.5"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0"}, "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts"}