import { HTMLProps, PolymorphicProps } from '../factory';
import { ForwardRefExoticComponent, RefAttributes } from 'react';
export interface PasswordInputInputBaseProps extends PolymorphicProps {
}
export interface PasswordInputInputProps extends HTMLProps<'input'>, PasswordInputInputBaseProps {
}
export declare const PasswordInputInput: ForwardRefExoticComponent<PasswordInputInputProps & RefAttributes<HTMLInputElement>>;
