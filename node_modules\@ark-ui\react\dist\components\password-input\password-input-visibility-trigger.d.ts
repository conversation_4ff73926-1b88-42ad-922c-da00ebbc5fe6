import { HTMLProps, PolymorphicProps } from '../factory';
import { ForwardRefExoticComponent, RefAttributes } from 'react';
export interface PasswordInputVisibilityTriggerBaseProps extends PolymorphicProps {
}
export interface PasswordInputVisibilityTriggerProps extends HTMLProps<'button'>, PasswordInputVisibilityTriggerBaseProps {
}
export declare const PasswordInputVisibilityTrigger: ForwardRefExoticComponent<PasswordInputVisibilityTriggerProps & RefAttributes<HTMLButtonElement>>;
