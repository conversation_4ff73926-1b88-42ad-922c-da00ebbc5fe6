import { HTMLProps, PolymorphicProps } from '../factory';
import { UsePasswordInputReturn } from './use-password-input';
import { ForwardRefExoticComponent, RefAttributes } from 'react';
interface RootProviderProps {
    value: UsePasswordInputReturn;
}
export interface PasswordInputRootProviderBaseProps extends RootProviderProps, PolymorphicProps {
}
export interface PasswordInputRootProviderProps extends HTMLProps<'div'>, PasswordInputRootProviderBaseProps {
}
export declare const PasswordInputRootProvider: ForwardRefExoticComponent<PasswordInputRootProviderProps & RefAttributes<HTMLDivElement>>;
export {};
