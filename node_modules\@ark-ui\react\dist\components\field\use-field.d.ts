import { HTMLProps } from '../factory';
import { RefObject } from 'react';
export interface ElementIds {
    root?: string | undefined;
    control?: string | undefined;
    label?: string | undefined;
    errorText?: string | undefined;
    helperText?: string | undefined;
}
export interface UseFieldProps {
    /**
     * The id of the field.
     */
    id?: string | undefined;
    /**
     * The ids of the field parts.
     */
    ids?: ElementIds | undefined;
    /**
     * Indicates whether the field is required.
     */
    required?: boolean | undefined;
    /**
     * Indicates whether the field is disabled.
     */
    disabled?: boolean | undefined;
    /**
     * Indicates whether the field is invalid.
     */
    invalid?: boolean | undefined;
    /**
     * Indicates whether the field is read-only.
     */
    readOnly?: boolean | undefined;
}
export type UseFieldReturn = ReturnType<typeof useField>;
export declare const useField: (props?: UseFieldProps) => {
    ariaDescribedby: string | undefined;
    ids: {
        root: string;
        control: string;
        label: string;
        errorText: string;
        helperText: string;
    };
    refs: {
        rootRef: RefObject<HTMLDivElement | null>;
    };
    disabled: boolean;
    invalid: boolean;
    readOnly: boolean;
    required: boolean;
    getLabelProps: () => HTMLProps<"label">;
    getRootProps: () => HTMLProps<"div">;
    getInputProps: () => HTMLProps<"input">;
    getTextareaProps: () => HTMLProps<"textarea">;
    getSelectProps: () => HTMLProps<"select">;
    getHelperTextProps: () => HTMLProps<"span">;
    getErrorTextProps: () => HTMLProps<"span">;
    getRequiredIndicatorProps: () => HTMLProps<"span">;
};
