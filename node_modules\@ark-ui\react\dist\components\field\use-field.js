'use client';
import { dataAttr, ariaAttr } from '@zag-js/dom-query';
import { useState, useId, useRef, useMemo } from 'react';
import { useEnvironmentContext } from '../../providers/environment/use-environment-context.js';
import { useSafeLayoutEffect } from '../../utils/use-safe-layout-effect.js';
import { useFieldsetContext } from '../fieldset/use-fieldset-context.js';
import { parts } from './field.anatomy.js';

const useField = (props = {}) => {
  const fieldset = useFieldsetContext();
  const env = useEnvironmentContext();
  const { ids, disabled = Boolean(fieldset?.disabled), invalid = false, readOnly = false, required = false } = props;
  const [hasErrorText, setHasErrorText] = useState(false);
  const [hasHelperText, setHasHelperText] = useState(false);
  const id = props.id ?? useId();
  const rootRef = useRef(null);
  const rootId = ids?.control ?? `field::${id}`;
  const errorTextId = ids?.errorText ?? `field::${id}::error-text`;
  const helperTextId = ids?.helperText ?? `field::${id}::helper-text`;
  const labelId = ids?.label ?? `field::${id}::label`;
  useSafeLayoutEffect(() => {
    const rootNode = rootRef.current;
    if (!rootNode) return;
    const checkTextElements = () => {
      const docOrShadowRoot = env.getRootNode();
      setHasErrorText(!!docOrShadowRoot.getElementById(errorTextId));
      setHasHelperText(!!docOrShadowRoot.getElementById(helperTextId));
    };
    checkTextElements();
    const win = env.getWindow();
    const observer = new win.MutationObserver(checkTextElements);
    observer.observe(rootNode, { childList: true, subtree: true });
    return () => observer.disconnect();
  }, [env, errorTextId, helperTextId]);
  const labelIds = useMemo(() => {
    const ids2 = [];
    if (hasErrorText && invalid) ids2.push(errorTextId);
    if (hasHelperText) ids2.push(helperTextId);
    return ids2.join(" ") || void 0;
  }, [invalid, errorTextId, helperTextId, hasErrorText, hasHelperText]);
  const getRootProps = useMemo(
    () => () => ({
      ...parts.root.attrs,
      id: rootId,
      ref: rootRef,
      role: "group",
      "data-disabled": dataAttr(disabled),
      "data-invalid": dataAttr(invalid),
      "data-readonly": dataAttr(readOnly)
    }),
    [disabled, invalid, readOnly, rootId]
  );
  const getLabelProps = useMemo(
    () => () => ({
      ...parts.label.attrs,
      id: labelId,
      "data-disabled": dataAttr(disabled),
      "data-invalid": dataAttr(invalid),
      "data-readonly": dataAttr(readOnly),
      htmlFor: id
    }),
    [disabled, invalid, readOnly, id, labelId]
  );
  const getControlProps = useMemo(
    () => () => ({
      "aria-describedby": labelIds,
      "aria-invalid": ariaAttr(invalid),
      "data-invalid": dataAttr(invalid),
      "data-required": dataAttr(required),
      "data-readonly": dataAttr(readOnly),
      id,
      required,
      disabled,
      readOnly
    }),
    [labelIds, invalid, required, readOnly, id, disabled]
  );
  const getInputProps = useMemo(
    () => () => ({
      ...getControlProps(),
      ...parts.input.attrs
    }),
    [getControlProps]
  );
  const getTextareaProps = useMemo(
    () => () => ({
      ...getControlProps(),
      ...parts.textarea.attrs
    }),
    [getControlProps]
  );
  const getSelectProps = useMemo(
    () => () => ({
      ...getControlProps(),
      ...parts.select.attrs
    }),
    [getControlProps]
  );
  const getHelperTextProps = useMemo(
    () => () => ({
      id: helperTextId,
      ...parts.helperText.attrs,
      "data-disabled": dataAttr(disabled)
    }),
    [disabled, helperTextId]
  );
  const getErrorTextProps = useMemo(
    () => () => ({
      id: errorTextId,
      ...parts.errorText.attrs,
      "aria-live": "polite"
    }),
    [errorTextId]
  );
  const getRequiredIndicatorProps = useMemo(
    () => () => ({
      "aria-hidden": true,
      ...parts.requiredIndicator.attrs
    }),
    []
  );
  return {
    ariaDescribedby: labelIds,
    ids: {
      root: rootId,
      control: id,
      label: labelId,
      errorText: errorTextId,
      helperText: helperTextId
    },
    refs: {
      rootRef
    },
    disabled,
    invalid,
    readOnly,
    required,
    getLabelProps,
    getRootProps,
    getInputProps,
    getTextareaProps,
    getSelectProps,
    getHelperTextProps,
    getErrorTextProps,
    getRequiredIndicatorProps
  };
};

export { useField };
