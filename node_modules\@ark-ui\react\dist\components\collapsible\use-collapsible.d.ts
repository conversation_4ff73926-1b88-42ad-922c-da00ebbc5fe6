import { PropTypes } from '@zag-js/react';
import { Optional } from '../../types';
import { RenderStrategyProps } from '../../utils/render-strategy';
import * as collapsible from '@zag-js/collapsible';
export interface UseCollapsibleProps extends Optional<Omit<collapsible.Props, 'dir' | 'getRootNode'>, 'id'>, RenderStrategyProps {
}
export interface UseCollapsibleReturn extends collapsible.Api<PropTypes> {
    /**
     * Whether the content is unmounted
     */
    isUnmounted?: boolean | undefined;
}
export declare const useCollapsible: (props?: UseCollapsibleProps) => UseCollapsibleReturn;
