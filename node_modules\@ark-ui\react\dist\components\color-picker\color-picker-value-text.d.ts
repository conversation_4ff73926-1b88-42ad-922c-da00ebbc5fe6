import { ColorStringFormat } from '@zag-js/color-utils';
import { HTMLProps, PolymorphicProps } from '../factory';
import { ForwardRefExoticComponent, RefAttributes } from 'react';
export interface ColorPickerValueTextBaseProps extends PolymorphicProps {
    format?: ColorStringFormat | undefined;
}
export interface ColorPickerValueTextProps extends HTMLProps<'span'>, ColorPickerValueTextBaseProps {
}
export declare const ColorPickerValueText: ForwardRefExoticComponent<ColorPickerValueTextProps & RefAttributes<HTMLDivElement>>;
