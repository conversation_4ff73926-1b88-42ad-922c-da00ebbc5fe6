import { HTMLProps, PolymorphicProps } from '../factory';
import { ForwardRefExoticComponent, RefAttributes } from 'react';
export interface PasswordInputIndicatorBaseProps extends PolymorphicProps {
    /**
     * The fallback content to display when the password is not visible.
     */
    fallback?: React.ReactNode;
}
export interface PasswordInputIndicatorProps extends HTMLProps<'span'>, PasswordInputIndicatorBaseProps {
}
export declare const PasswordInputIndicator: ForwardRefExoticComponent<PasswordInputIndicatorProps & RefAttributes<HTMLSpanElement>>;
