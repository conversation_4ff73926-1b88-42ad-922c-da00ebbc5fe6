import { HTMLProps } from '../factory';
import { RefObject } from 'react';
export interface UseFieldsetProps {
    /**
     * The id of the fieldset.
     */
    id?: string | undefined;
    /**
     * Indicates whether the fieldset is disabled.
     */
    disabled?: boolean | undefined;
    /**
     * Indicates whether the fieldset is invalid.
     */
    invalid?: boolean | undefined;
}
export type UseFieldsetReturn = ReturnType<typeof useFieldset>;
export declare const useFieldset: (props?: UseFieldsetProps) => {
    refs: {
        rootRef: RefObject<HTMLFieldSetElement | null>;
    };
    disabled: boolean;
    invalid: boolean;
    getRootProps: () => HTMLProps<"fieldset">;
    getLegendProps: () => HTMLProps<"legend">;
    getHelperTextProps: () => HTMLProps<"span">;
    getErrorTextProps: () => HTMLProps<"span">;
};
