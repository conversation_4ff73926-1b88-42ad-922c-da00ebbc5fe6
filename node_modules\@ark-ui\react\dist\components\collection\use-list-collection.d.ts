import { CollectionOptions, ListCollection } from './list-collection';
export interface UseListCollectionProps<T> extends Omit<CollectionOptions<T>, 'items'> {
    /**
     * The initial items to display in the collection.
     */
    initialItems: T[];
    /**
     * The filter function to use to filter the items.
     */
    filter?: (itemText: string, filterText: string, item: T) => boolean;
    /**
     * The maximum number of items to display in the collection.
     * Useful for performance when you have a large number of items.
     */
    limit?: number;
}
export declare function useListCollection<T>(props: UseListCollectionProps<T>): UseListCollectionReturn<T>;
export interface UseListCollectionReturn<T> {
    /**
     * The collection of items.
     */
    collection: ListCollection<T>;
    /**
     * The function to filter the items.
     */
    filter: (inputValue: string) => void;
    /**
     * The function to set the items.
     */
    set: (items: T[]) => void;
    /**
     * The function to reset the items.
     */
    reset: () => void;
    /**
     * The function to clear the items.
     */
    clear: () => void;
    /**
     * The function to insert items at a specific index.
     */
    insert: (index: number, ...items: T[]) => void;
    /**
     * The function to insert items before a specific value.
     */
    insertBefore: (value: string, ...items: T[]) => void;
    /**
     * The function to insert items after a specific value.
     */
    insertAfter: (value: string, ...items: T[]) => void;
    /**
     * The function to remove items.
     */
    remove: (...itemOrValues: Array<T | string>) => void;
    /**
     * The function to move an item to a specific index.
     */
    move: (value: string, to: number) => void;
    /**
     * The function to move an item before a specific value.
     */
    moveBefore: (value: string, ...values: string[]) => void;
    /**
     * The function to move an item after a specific value.
     */
    moveAfter: (value: string, ...values: string[]) => void;
    /**
     * The function to reorder items.
     */
    reorder: (from: number, to: number) => void;
    /**
     * The function to append items.
     */
    append: (...items: T[]) => void;
    /**
     * The function to upsert an item.
     */
    upsert: (value: string, item: T, mode?: 'append' | 'prepend') => void;
    /**
     * The function to prepend items.
     */
    prepend: (...items: T[]) => void;
    /**
     * The function to update an item.
     */
    update: (value: string, item: T) => void;
}
