import { Assign } from '../../types';
import { HTMLProps, PolymorphicProps } from '../factory';
import { UsePasswordInputProps } from './use-password-input';
import { ForwardRefExoticComponent, RefAttributes } from 'react';
export interface PasswordInputRootBaseProps extends UsePasswordInputProps, PolymorphicProps {
}
export interface PasswordInputRootProps extends Assign<HTMLProps<'div'>, PasswordInputRootBaseProps> {
}
export declare const PasswordInputRoot: ForwardRefExoticComponent<PasswordInputRootProps & RefAttributes<HTMLDivElement>>;
