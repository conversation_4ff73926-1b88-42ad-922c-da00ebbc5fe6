export type { OpenChangeDetails as FloatingPanelOpenChangeDetails, PositionChangeDetails as FloatingPanelPositionChangeDetails, SizeChangeDetails as FloatingPanelSizeChangeDetails, StageChangeDetails as FloatingPanelStageChangeDetails, } from '@zag-js/floating-panel';
export { FloatingPanelBody, type FloatingPanelBodyProps, type FloatingPanelBodyBaseProps } from './floating-panel-body';
export { FloatingPanelCloseTrigger, type FloatingPanelCloseTriggerProps, type FloatingPanelCloseTriggerBaseProps, } from './floating-panel-close-trigger';
export { FloatingPanelContent, type FloatingPanelContentProps, type FloatingPanelContentBaseProps, } from './floating-panel-content';
export { FloatingPanelContext, type FloatingPanelContextProps } from './floating-panel-context';
export { FloatingPanelDragTrigger, type FloatingPanelDragTriggerProps, type FloatingPanelDragTriggerBaseProps, } from './floating-panel-drag-trigger';
export { FloatingPanelHeader, type FloatingPanelHeaderProps, type FloatingPanelHeaderBaseProps, } from './floating-panel-header';
export { FloatingPanelPositioner, type FloatingPanelPositionerProps, type FloatingPanelPositionerBaseProps, } from './floating-panel-positioner';
export { FloatingPanelResizeTrigger, type FloatingPanelResizeTriggerProps, type FloatingPanelResizeTriggerBaseProps, } from './floating-panel-resize-trigger';
export { FloatingPanelStageTrigger, type FloatingPanelStageTriggerProps, type FloatingPanelStageTriggerBaseProps, } from './floating-panel-stage-trigger';
export { FloatingPanelRoot, type FloatingPanelRootProps, type FloatingPanelRootBaseProps } from './floating-panel-root';
export { FloatingPanelRootProvider, type FloatingPanelRootProviderProps, type FloatingPanelRootProviderBaseProps, } from './floating-panel-root-provider';
export { FloatingPanelTitle, type FloatingPanelTitleProps, type FloatingPanelTitleBaseProps, } from './floating-panel-title';
export { FloatingPanelTrigger, type FloatingPanelTriggerProps, type FloatingPanelTriggerBaseProps, } from './floating-panel-trigger';
export { FloatingPanelControl, type FloatingPanelControlProps, type FloatingPanelControlBaseProps, } from './floating-panel-control';
export { useFloatingPanel, type UseFloatingPanelProps, type UseFloatingPanelReturn } from './use-floating-panel';
export { useFloatingPanelContext, type UseFloatingPanelContext } from './use-floating-panel-context';
export { floatingPanelAnatomy } from './floating-panel.anatomy';
export * as FloatingPanel from './floating-panel';
